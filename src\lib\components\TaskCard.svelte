<script lang="ts">
  import Badge from './ui/Badge.svelte';
  import Button from './ui/Button.svelte';
  import { createEventDispatcher } from 'svelte';

  export let task: any;
  export let categories: any[] = [];
  export let compact: boolean = false; // 新增：紧凑模式
  export let isToggling: boolean = false; // 新增：切换状态

  const dispatch = createEventDispatcher();

  let showActions = false;
  let longPressTimer: number;

  function getCategoryName(categoryId: string | null) {
    if (!categoryId) return null;
    const category = categories.find(c => c.id === categoryId);
    return category?.name || null;
  }

  function getCategoryColor(categoryId: string | null): string | null {
    if (!categoryId) return null;
    const category = categories.find(c => c.id === categoryId);
    return category?.color || null;
  }

  function hexToRgba(hex: string, alpha: number = 1): string {
    const h = hex?.trim()?.replace('#', '') ?? '';
    if (h.length !== 6) return `rgba(100,116,139,${alpha})`;
    const r = parseInt(h.slice(0, 2), 16);
    const g = parseInt(h.slice(2, 4), 16);
    const b = parseInt(h.slice(4, 6), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  }

  function getPriorityLabel(priority: number) {
    switch (priority) {
      case 3: return 'High';
      case 2: return 'Normal';
      case 1: return 'Low';
      default: return 'Normal';
    }
  }

  function getPriorityVariant(priority: number): 'danger' | 'warning' | 'primary' {
    switch (priority) {
      case 3: return 'danger';
      case 2: return 'warning';
      case 1: return 'primary';
      default: return 'warning';
    }
  }

  function getPriorityColor(priority: number): string {
    switch (priority) {
      case 3: return '#ef4444'; // red
      case 2: return '#f59e0b'; // yellow
      case 1: return '#3b82f6'; // blue
      default: return '#6b7280'; // gray
    }
  }

  function formatDate(dateString: string | null) {
    if (!dateString) return 'No due date';
    const date = new Date(dateString);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const taskDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

    if (taskDate.getTime() === today.getTime()) {
      return 'Today';
    } else if (taskDate.getTime() === today.getTime() + 24 * 60 * 60 * 1000) {
      return 'Tomorrow';
    } else if (taskDate.getTime() === today.getTime() - 24 * 60 * 60 * 1000) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString();
    }
  }

  function formatCompactDate(dateString: string | null) {
    if (!dateString) return 'No due date';
    const date = new Date(dateString);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const taskDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const diffTime = taskDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays === -1) return 'Yesterday';
    if (diffDays > 0 && diffDays <= 7) return `${diffDays} day${diffDays > 1 ? 's' : ''}`;
    if (diffDays > 7 && diffDays <= 30) return `${Math.ceil(diffDays/7)} week${Math.ceil(diffDays/7) > 1 ? 's' : ''}`;
    if (diffDays < 0) return `${Math.abs(diffDays)} day${Math.abs(diffDays) > 1 ? 's' : ''} overdue`;
    return `${Math.ceil(diffDays/30)} month${Math.ceil(diffDays/30) > 1 ? 's' : ''}`;
  }

  function isOverdue(dateString: string | null) {
    if (!dateString) return false;
    const date = new Date(dateString);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const taskDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    return taskDate.getTime() < today.getTime();
  }

  function isDueSoon(dateString: string | null): boolean {
    if (!dateString) return false;
    const now = new Date();
    const due = new Date(dateString);
    return due.getTime() >= now.getTime() && (due.getTime() - now.getTime()) <= 24 * 60 * 60 * 1000;
  }

  function handleToggle() {
    dispatch('toggle', task.id);
  }

  function handleEdit() {
    dispatch('edit', task.id);
  }

  function handleDelete() {
    dispatch('delete', task.id);
  }

  function handleLongPressStart() {
    longPressTimer = setTimeout(() => {
      showActions = true;
    }, 500); // 500ms long press
  }

  function handleLongPressEnd() {
    clearTimeout(longPressTimer);
  }

  // Cancel long-press when finger moves (avoid triggering while scrolling)
  function handleTouchMove() {
    clearTimeout(longPressTimer);
  }

  // Prevent native context menu on long-press
  function preventContextMenu(event: Event) {
    event.preventDefault();
  }

  // If actions are visible due to long-press, block link navigation on release
  function handleTitleClick(event: MouseEvent) {
    if (showActions) {
      event.preventDefault();
      event.stopPropagation();
    }
  }

  function hideActions() {
    showActions = false;
  }
</script>

{#if compact}
  <!-- Compact Mobile Card -->
  <div
    class="compact-task-card"
    class:opacity-70={task.completed}
    class:actions-visible={showActions}
    role="button"
    tabindex="0"
    on:touchstart={handleLongPressStart}
    on:touchend={handleLongPressEnd}
    on:touchmove={handleTouchMove}
    on:mousedown={handleLongPressStart}
    on:mouseup={handleLongPressEnd}
    on:mouseleave={handleLongPressEnd}
    on:contextmenu={preventContextMenu}
  >
    {#if showActions}
      <!-- Action Buttons -->
      <div class="action-buttons">
        <button class="action-btn edit-btn" on:click={handleEdit} aria-label="Edit task">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        </button>
        <button class="action-btn delete-btn" on:click={handleDelete} aria-label="Delete task">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
        <button class="action-btn cancel-btn" on:click={hideActions} aria-label="Cancel">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    {:else}
      <!-- Task Content - Two Lines Max -->
      <div class="task-content-wrapper">
        <input
          type="checkbox"
          class="task-checkbox"
          class:opacity-50={isToggling}
          checked={task.completed}
          disabled={isToggling}
          on:change={handleToggle}
        />
        <div class="task-info">
          <!-- Line 1: Task Title -->
          <a href="/dashboard/tasks/{task.id}"
             class="task-title-link"
             on:click={handleTitleClick}
             on:contextmenu={preventContextMenu}
          >
            <span class="task-title" class:line-through={task.completed}>
              {task.title}
            </span>
          </a>

          <!-- Line 2: Category Badge + Due Date (left/right) -->
          <div class="task-meta-line justify-between">
            <div class="flex items-center gap-2 min-w-[10ch] flex-1 pr-2">
              {#if getCategoryName(task.categoryId)}
                <Badge
                  size="sm"
                  class="!px-2 !py-0.5 !rounded-full !text-[10px] !font-medium max-w-full truncate"
                  style={`background-color: ${hexToRgba(getCategoryColor(task.categoryId) ?? '#64748b', 0.18)}; color: ${getCategoryColor(task.categoryId) ?? '#64748b'};`}
                  aria-label="Category: {getCategoryName(task.categoryId)}"
                >
                  {getCategoryName(task.categoryId)}
                </Badge>
              {/if}
            </div>
            <div class="flex items-center gap-1 flex-shrink-0">
              <svg class="w-3.5 h-3.5 {isOverdue(task.dueDate) ? 'text-red-600' : isDueSoon(task.dueDate) ? 'text-yellow-600' : 'text-secondary-500'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <span class="due-date-text" class:overdue={isOverdue(task.dueDate)}>
                {formatCompactDate(task.dueDate)}
              </span>
            </div>
          </div>
        </div>
      </div>
    {/if}
  </div>
{:else}
  <!-- Original Card Layout -->
  <div class="mobile-card" class:opacity-70={task.completed}>
    <!-- Task Header -->
    <div class="flex items-start gap-3">
      <input
        type="checkbox"
        class="mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
        class:opacity-50={isToggling}
        checked={task.completed}
        disabled={isToggling}
        on:change={handleToggle}
      />
      <div class="flex-1 min-w-0">
        <a href="/dashboard/tasks/{task.id}" class="block">
          <h3 class="text-sm font-medium text-secondary-900 hover:text-primary-600 transition-colors"
              class:line-through={task.completed}>
            {task.title}
          </h3>
          {#if task.subtitle}
            <p class="text-xs text-secondary-500 mt-1">{task.subtitle}</p>
          {/if}
        </a>
      </div>
    </div>

    <!-- Task Details -->
    <div class="space-y-2 mt-3">
      <div class="mobile-card-row">
        <span class="mobile-card-label">Priority</span>
        <Badge variant={getPriorityVariant(task.priority)} size="sm">
          {getPriorityLabel(task.priority)}
        </Badge>
      </div>

      <div class="mobile-card-row">
        <span class="mobile-card-label">Due Date</span>
        <span class="mobile-card-value">{formatDate(task.dueDate)}</span>
      </div>

      {#if getCategoryName(task.categoryId)}
        <div class="mobile-card-row">
          <span class="mobile-card-label">Category</span>
          <Badge variant="secondary" size="sm">
            {getCategoryName(task.categoryId)}
          </Badge>
        </div>
      {/if}
    </div>

    <!-- Actions -->
    <div class="flex justify-end gap-2 mt-4 pt-3 border-t border-secondary-100">
      <Button variant="ghost" size="sm" on:click={handleEdit}>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
        Edit
      </Button>
      <Button variant="ghost" size="sm" on:click={handleDelete} class="text-danger-600 hover:text-danger-700 hover:bg-danger-50">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
        Delete
      </Button>
    </div>
  </div>
{/if}

<style>
  .compact-task-card {
    @apply bg-white rounded-lg border border-gray-200 px-3 py-2.5 hover:shadow-md transition-all duration-200;
  }

  .task-content-wrapper {
    @apply flex items-center gap-3;
  }

  .task-checkbox {
    @apply h-5 w-5 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded flex-shrink-0;
  }

  .task-info {
    @apply flex-1 min-w-0 space-y-0.5;
  }

  .task-title-link {
    @apply block;
    -webkit-touch-callout: none; /* disable long-press callout on iOS/Chrome */
  }

  .task-title {
    @apply text-sm font-medium text-secondary-900 hover:text-primary-600 transition-colors leading-tight;
  }

  .task-meta-line {
    @apply flex items-center;
  }

  .due-date-text {
    @apply text-xs font-medium text-gray-600;
  }

  .due-date-text.overdue {
    @apply text-red-600;
  }

  .action-buttons {
    @apply flex items-center justify-center gap-4 w-full;
  }

  .action-btn {
    @apply w-10 h-10 rounded-full flex items-center justify-center transition-colors;
  }

  .edit-btn {
    @apply bg-blue-100 text-blue-600 hover:bg-blue-200;
  }

  .delete-btn {
    @apply bg-red-100 text-red-600 hover:bg-red-200;
  }

  .cancel-btn {
    @apply bg-gray-100 text-gray-600 hover:bg-gray-200;
  }

  .actions-visible {
    @apply bg-gray-50;
  }
</style>
